package internetal

import (
	"encoding/json"
	"time"
)

// 知识库主表
type KnowledgeBase struct {
	ID           string    `gorm:"primaryKey;size:50" json:"id"`
	Name         string    `gorm:"size:200;not null" json:"name"`
	Description  string    `gorm:"type:text" json:"description"`
	Type         string    `gorm:"size:50;not null" json:"type"` // text, table, image
	Status       string    `gorm:"size:50;default:processing" json:"status"` // processing, completed, failed
	SegmentCount int       `gorm:"default:0" json:"segmentCount"`
	CreatedAt    time.Time `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt    time.Time `gorm:"autoUpdateTime" json:"updatedAt"`
	
	// 关联关系
	Files    []KnowledgeBaseFile    `gorm:"foreignKey:KnowledgeBaseID;constraint:OnDelete:CASCADE" json:"files"`
	Settings KnowledgeBaseSettings `gorm:"foreignKey:KnowledgeBaseID;constraint:OnDelete:CASCADE" json:"settings"`
}

// 知识库文件表
type KnowledgeBaseFile struct {
	ID               string    `gorm:"primaryKey;size:50" json:"id"`
	KnowledgeBaseID  string    `gorm:"size:50;not null;index" json:"knowledgeBaseId"`
	Name             string    `gorm:"size:255;not null" json:"name"`
	Size             int64     `gorm:"not null" json:"size"`
	Content          string    `gorm:"type:longtext" json:"content"`
	Status           string    `gorm:"size:50;default:processing" json:"status"` // processing, completed, failed
	Progress         int       `gorm:"default:0" json:"progress"`
	Error            string    `gorm:"type:text" json:"error,omitempty"`
	AIEnhanced       bool      `gorm:"default:false" json:"aiEnhanced"`
	CreatedAt        time.Time `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt        time.Time `gorm:"autoUpdateTime" json:"updatedAt"`
	
	// 关联关系
	Segments []KnowledgeSegment `gorm:"foreignKey:FileID;constraint:OnDelete:CASCADE" json:"segments"`
}

// 知识库分段表
type KnowledgeSegment struct {
	ID               string    `gorm:"primaryKey;size:50" json:"id"`
	FileID           string    `gorm:"size:50;not null;index" json:"fileId"`
	KnowledgeBaseID  string    `gorm:"size:50;not null;index" json:"knowledgeBaseId"`
	Content          string    `gorm:"type:text;not null" json:"content"`
	Keywords         string    `gorm:"type:text" json:"keywords"` // JSON数组存储
	Summary          string    `gorm:"type:text" json:"summary"`
	Length           int       `gorm:"not null" json:"length"`
	Position         int       `gorm:"not null" json:"position"` // 在文件中的位置
	Embedding        string    `gorm:"type:longtext" json:"embedding,omitempty"` // 向量数据，JSON格式
	CreatedAt        time.Time `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt        time.Time `gorm:"autoUpdateTime" json:"updatedAt"`
}

// 知识库设置表
type KnowledgeBaseSettings struct {
	ID               uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	KnowledgeBaseID  string    `gorm:"size:50;not null;uniqueIndex" json:"knowledgeBaseId"`
	ParseText        bool      `gorm:"default:true" json:"parseText"`
	ParseTable       bool      `gorm:"default:false" json:"parseTable"`
	ContentFilter    string    `gorm:"type:text" json:"contentFilter"`
	SegmentStrategy  string    `gorm:"size:50;default:auto" json:"segmentStrategy"`
	SegmentLength    int       `gorm:"default:1000" json:"segmentLength"`
	SegmentOverlap   int       `gorm:"default:100" json:"segmentOverlap"`
	Separator        string    `gorm:"size:50" json:"separator"`
	ExtractKeywords  bool      `gorm:"default:true" json:"extractKeywords"`
	GenerateSummary  bool      `gorm:"default:false" json:"generateSummary"`
	UseAI            bool      `gorm:"default:true" json:"useAI"`
	FallbackToLocal  bool      `gorm:"default:true" json:"fallbackToLocal"`
	CreatedAt        time.Time `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt        time.Time `gorm:"autoUpdateTime" json:"updatedAt"`
}

// 知识库搜索结果（用于搜索API）
type KnowledgeSearchResult struct {
	ID              string   `json:"id"`
	Content         string   `json:"content"`
	Score           float64  `json:"score"`
	Source          string   `json:"source"`
	KnowledgeBaseId string   `json:"knowledgeBaseId"`
	Keywords        []string `json:"keywords"`
	Summary         string   `json:"summary"`
}

// 辅助方法：将Keywords字符串转换为数组
func (ks *KnowledgeSegment) GetKeywords() []string {
	if ks.Keywords == "" {
		return []string{}
	}
	var keywords []string
	json.Unmarshal([]byte(ks.Keywords), &keywords)
	return keywords
}

// 辅助方法：设置Keywords数组
func (ks *KnowledgeSegment) SetKeywords(keywords []string) {
	if keywords == nil {
		keywords = []string{}
	}
	data, _ := json.Marshal(keywords)
	ks.Keywords = string(data)
}

// 辅助方法：获取向量数据
func (ks *KnowledgeSegment) GetEmbedding() []float64 {
	if ks.Embedding == "" {
		return []float64{}
	}
	var embedding []float64
	json.Unmarshal([]byte(ks.Embedding), &embedding)
	return embedding
}

// 辅助方法：设置向量数据
func (ks *KnowledgeSegment) SetEmbedding(embedding []float64) {
	if embedding == nil {
		embedding = []float64{}
	}
	data, _ := json.Marshal(embedding)
	ks.Embedding = string(data)
}

// 表名设置
func (KnowledgeBase) TableName() string {
	return "knowledge_bases"
}

func (KnowledgeBaseFile) TableName() string {
	return "knowledge_base_files"
}

func (KnowledgeSegment) TableName() string {
	return "knowledge_segments"
}

func (KnowledgeBaseSettings) TableName() string {
	return "knowledge_base_settings"
}
