# AI Assistant Backend 使用指南

## 🚀 快速启动

### 1. 环境要求
- Go 1.24.5+
- MySQL 5.7+
- Git

### 2. 数据库配置
编辑 `backend/cmd/config.yaml` 文件：
```yaml
database:
  username: root
  password: 123456
  host: localhost
  port: 3306
  base: myapp_db
```

### 3. 启动服务

#### Windows
```bash
# 方式1: 使用启动脚本
cd backend
start.bat

# 方式2: 手动启动
cd backend/cmd
go run .
```

#### Linux/Mac
```bash
cd backend/cmd
go run .
```

## 📋 功能模块

### ✅ 已移除 simple_server.go
- **原因**: 功能重复，维护成本高
- **解决方案**: 统一使用 `main.go` 实现完整功能

### 🔧 主要功能
1. **机器人管理** - 完整的CRUD操作，数据库持久化
2. **知识库管理** - 文档分析、智能分割、关键词提取
3. **LLM交互** - AI对话功能，支持多种模型
4. **语音功能** - TTS、语音包管理
5. **WebSocket** - 实时通信支持
6. **历史记录** - 对话历史管理

### 🛠️ API接口

#### 机器人管理
- `POST /api/robot/create` - 创建机器人
- `POST /api/robot/find/by_id` - 根据ID查找
- `POST /api/robot/find/by_name` - 根据名称查找
- `POST /api/robot/update` - 更新机器人
- `POST /api/robot/delete` - 删除机器人
- `POST /api/robot/list` - 获取机器人列表

#### 知识库管理
- `POST /api/knowledge-base/create` - 创建知识库
- `POST /api/knowledge-base/search` - 搜索知识库
- `POST /api/knowledge-base/analyze-document` - 文档分析

#### LLM交互
- `POST /api/llm/communication` - AI对话
- `POST /api/text/send` - 发送文本消息

#### 语音功能
- `POST /api/voice/settings` - 语音设置
- `GET /api/voice/settings` - 获取语音设置
- `POST /api/tts/test` - TTS测试

#### WebSocket
- `GET /ws` - WebSocket连接

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   ```
   解决方案: 检查 config.yaml 中的数据库配置
   确保MySQL服务正在运行
   ```

2. **端口占用**
   ```
   解决方案: 修改 main.go 中的端口号
   或者停止占用8080端口的其他服务
   ```

3. **依赖包问题**
   ```bash
   cd backend/cmd
   go mod tidy
   go mod download
   ```

### 开发建议

1. **测试API**
   ```bash
   # 使用curl测试机器人列表
   curl -X POST http://localhost:8080/api/robot/list \
        -H "Content-Type: application/json"
   ```

2. **查看日志**
   - 服务器启动时会显示详细的初始化信息
   - 数据库连接状态会在控制台输出

3. **数据库管理**
   - 数据库表会自动创建和迁移
   - 首次启动会创建默认机器人

## 📝 更新日志

### v1.0 (当前版本)
- ✅ 移除了 simple_server.go
- ✅ 统一使用数据库存储
- ✅ 修复了API接口一致性问题
- ✅ 清理了重复的初始化代码
- ✅ 优化了错误处理和日志输出

### 下一步计划
- [ ] 添加API文档生成
- [ ] 增加单元测试覆盖率
- [ ] 优化数据库查询性能
- [ ] 添加配置热重载功能
