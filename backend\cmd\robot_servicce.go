package main

import (
	"backend/internetal"
	"time"
)

func CreateRobotService(robot internetal.Robot) (internetal.Robot, error) {
	// 设置默认值
	if robot.PersonnelDesign == "" {
		robot.PersonnelDesign = "你是一个友善、专业的AI助手。"
	}
	if robot.ReplyLogic == "" {
		robot.ReplyLogic = `{"systemRole":"你是一个专业的AI助手","dialogCommand":"请以简洁、专业的方式回答用户的问题","responseMode":"simple","temperature":0.6,"maxTokens":50,"language":"zh-cn","speaker":"601002","speechSpeed":1.0}`
	}
	if robot.KnowledgeConfig == "" {
		robot.KnowledgeConfig = `{"callMethod":"auto","searchStrategy":"mixed","maxRecall":5,"minScore":0.50,"queryRewrite":true,"resultRerank":true}`
	}

	robot.CreateTime = time.Now()
	robot.UpdateTime = time.Now()

	// 保存到数据库
	result := SqlSession.Create(&robot)
	return robot, result.Error
}

func DeleteRobotService(id int) error {
	result := SqlSession.Delete(&internetal.Robot{}, id)
	return result.Error
}

func FindByIdService(id int) (internetal.Robot, error) {
	var robot internetal.Robot
	result := SqlSession.First(&robot, id)
	return robot, result.Error
}

func FindByNameService(name string) (internetal.Robot, error) {
	var robot internetal.Robot
	result := SqlSession.Where("name = ?", name).First(&robot)
	return robot, result.Error
}

func UpdateRobotService(robot internetal.Robot) error {
	var findAssistant internetal.Robot
	result := SqlSession.First(&findAssistant, robot.ID)
	if result.Error != nil {
		return result.Error
	}
	if robot.Name == "" {
		robot.Name = findAssistant.Name
	}
	if robot.Description == "" {
		robot.Description = findAssistant.Description
	}
	if robot.PersonnelDesign == "" {
		robot.PersonnelDesign = findAssistant.PersonnelDesign
	}
	robot.UpdateTime = time.Now()
	return SqlSession.Model(&findAssistant).Updates(robot).Error
}

func ListRobotsService() ([]internetal.Robot, error) {
	var robots []internetal.Robot

	// 从数据库查询所有机器人
	result := SqlSession.Find(&robots)
	if result.Error != nil {
		return nil, result.Error
	}

	// 如果数据库中没有机器人，创建一个默认的
	if len(robots) == 0 {
		defaultRobot := internetal.Robot{
			Name:            "默认助手",
			Description:     "默认AI助手",
			PersonnelDesign: "你是一个友善、专业的AI助手。",
			ReplyLogic:      `{"systemRole":"你是一个专业的AI助手","dialogCommand":"请以简洁、专业的方式回答用户的问题","responseMode":"simple","temperature":0.6,"maxTokens":50,"language":"zh-cn","speaker":"601002","speechSpeed":1.0}`,
			KnowledgeConfig: `{"callMethod":"auto","searchStrategy":"mixed","maxRecall":5,"minScore":0.50,"queryRewrite":true,"resultRerank":true}`,
			CreateTime:      time.Now(),
			UpdateTime:      time.Now(),
		}

		// 保存默认机器人到数据库
		createResult := SqlSession.Create(&defaultRobot)
		if createResult.Error != nil {
			return nil, createResult.Error
		}

		robots = append(robots, defaultRobot)
	}

	return robots, nil
}
