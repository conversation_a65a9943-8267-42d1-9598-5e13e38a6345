package main

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// 优化请求结构
type OptimizeRequest struct {
	Mode               string                 `json:"mode"`               // auto 或 custom
	Input              string                 `json:"input"`              // 用户输入的优化要求
	CurrentPersonality string                 `json:"currentPersonality"` // 当前人设
	ReplyLogic         map[string]interface{} `json:"replyLogic"`         // 回复逻辑配置
}

// 优化响应结构
type OptimizeResponse struct {
	Code int    `json:"code"`
	Data string `json:"data"`
	Msg  string `json:"msg"`
}

// 提示词优化处理器
func OptimizePrompt(c *gin.Context) {
	var req OptimizeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, OptimizeResponse{
			Code: 400,
			Msg:  "请求参数错误: " + err.<PERSON>rror(),
		})
		return
	}

	// 生成优化后的提示词
	optimizedPrompt, err := generateOptimizedPrompt(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, OptimizeResponse{
			Code: 500,
			Msg:  "优化失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, OptimizeResponse{
		Code: 200,
		Data: optimizedPrompt,
		Msg:  "优化成功",
	})
}

// 生成优化后的提示词
func generateOptimizedPrompt(req OptimizeRequest) (string, error) {
	// 预定义的优化模板
	templates := map[string]string{
		"professional": `你是一位经验丰富的专业顾问，具备深厚的行业知识和丰富的实践经验。你的核心特质包括：

🎯 **专业能力**
- 拥有扎实的理论基础和丰富的实践经验
- 能够快速理解问题的本质和关键要素
- 提供准确、实用、可操作的解决方案

💬 **沟通风格**
- 保持专业而友善的态度
- 用清晰简洁的语言解释复杂概念
- 确保用户能够理解并应用建议

🔍 **工作方法**
- 系统性分析问题，提供结构化的回答
- 基于事实和数据给出建议
- 关注解决方案的可行性和效果`,

		"friendly": `你是一位温暖友善的AI助手，致力于为用户提供贴心的帮助和支持。你的核心特质包括：

😊 **性格特点**
- 温暖、耐心、善解人意
- 总是以积极乐观的态度面对问题
- 善于倾听并理解用户的需求和情感

💝 **交流方式**
- 语言亲切自然，让用户感到舒适
- 在适当的时候给予鼓励和支持
- 用温和的方式提供建议和指导

🤝 **服务理念**
- 以用户的感受为优先考虑
- 创造轻松愉快的交流氛围
- 让每一次互动都充满温暖和关怀`,

		"creative": `你是一位富有创意和想象力的AI助手，擅长用创新的思维解决问题。你的核心特质包括：

🎨 **创意思维**
- 善于从多个角度思考问题
- 提供新颖独特的解决方案
- 鼓励跳出常规思维框架

🌟 **表达方式**
- 运用比喻、故事和生动的例子
- 让交流变得有趣而富有启发性
- 用形象化的语言解释抽象概念

🚀 **工作风格**
- 探索更多可能性和潜在机会
- 激发用户的创造力和想象力
- 将复杂问题转化为有趣的挑战`,

		"technical": `你是一位技术专家，精通各种技术领域的知识和最佳实践。你的核心特质包括：

⚙️ **技术能力**
- 掌握深厚的技术知识和实践经验
- 了解最新的技术趋势和发展动态
- 能够解决复杂的技术问题

📚 **教学能力**
- 深入浅出地解释复杂的技术概念
- 根据用户技术水平调整解释深度
- 提供清晰的步骤指导和代码示例

🎯 **实用导向**
- 关注技术的实用性和可操作性
- 提供最佳实践和优化建议
- 帮助用户避免常见的技术陷阱`,
	}

	// 分析用户输入，确定优化方向
	input := strings.ToLower(req.Input)
	var selectedTemplate string
	var customization string

	// 根据关键词匹配模板
	if strings.Contains(input, "友善") || strings.Contains(input, "温和") || strings.Contains(input, "亲切") || strings.Contains(input, "温暖") {
		selectedTemplate = templates["friendly"]
		customization = "已针对友善温和的特质进行优化"
	} else if strings.Contains(input, "创意") || strings.Contains(input, "创新") || strings.Contains(input, "想象") || strings.Contains(input, "有趣") {
		selectedTemplate = templates["creative"]
		customization = "已针对创意创新的特质进行优化"
	} else if strings.Contains(input, "技术") || strings.Contains(input, "专业") || strings.Contains(input, "技能") || strings.Contains(input, "编程") {
		selectedTemplate = templates["technical"]
		customization = "已针对技术专业的特质进行优化"
	} else {
		selectedTemplate = templates["professional"]
		customization = "已针对专业能力进行优化"
	}

	// 如果有当前人设，尝试在其基础上优化
	if req.CurrentPersonality != "" {
		// 分析当前人设的特点，并结合用户要求进行优化
		optimizedPrompt := fmt.Sprintf("%s\n\n**个性化定制**\n根据您的要求\"%s\"，%s，使助手能够更好地满足您的期望。\n\n**原有设定参考**\n%s",
			selectedTemplate, req.Input, customization, req.CurrentPersonality)
		return optimizedPrompt, nil
	}

	// 如果没有当前人设，直接使用模板
	optimizedPrompt := fmt.Sprintf("%s\n\n**个性化定制**\n根据您的要求\"%s\"，%s，这将帮助助手更好地理解其角色定位。",
		selectedTemplate, req.Input, customization)

	return optimizedPrompt, nil
}

// 基于调试结果的优化（预留接口）
func OptimizeBasedOnDebug(c *gin.Context) {
	// 这里可以根据实际的调试数据进行更精准的优化
	// 暂时使用相同的逻辑
	OptimizePrompt(c)
}
