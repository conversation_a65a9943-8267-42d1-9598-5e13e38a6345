/**
 * AI服务配置管理
 * 管理阿里云百炼和其他AI服务的配置
 */

class AIConfig {
  constructor() {
    this.config = this.loadConfig()
  }

  /**
   * 默认配置
   */
  getDefaultConfig() {
    return {
      bailian: {
        accessKeyId: 'LTAI5tB38aLEpHToKESq9W3T',
        accessKeySecret: '******************************',
        workspaceId: 'llm-xz4u83kacijf8jaj',
        apiKey: 'sk-0baf684faf044823a593986c15617b12',
        enabled: true,
        timeout: 30000,
        retryCount: 2
      },
      processing: {
        useAI: true,
        fallbackToLocal: true,
        maxFileSize: 10 * 1024 * 1024, // 10MB
        batchSize: 5,
        concurrency: 2
      },
      segmentation: {
        defaultStrategy: 'auto',
        maxLength: 1000,
        overlap: 100,
        minSegmentLength: 50
      },
      ui: {
        showAIOptions: true,
        showProcessingDetails: true,
        autoSave: true,
        theme: 'light'
      }
    }
  }

  /**
   * 从本地存储加载配置
   */
  loadConfig() {
    try {
      const saved = localStorage.getItem('ai_config')
      if (saved) {
        const parsed = JSON.parse(saved)
        return this.mergeConfig(this.getDefaultConfig(), parsed)
      }
    } catch (error) {
      console.warn('加载AI配置失败:', error)
    }
    return this.getDefaultConfig()
  }

  /**
   * 保存配置到本地存储
   */
  saveConfig() {
    try {
      localStorage.setItem('ai_config', JSON.stringify(this.config))
      return true
    } catch (error) {
      console.error('保存AI配置失败:', error)
      return false
    }
  }

  /**
   * 合并配置
   */
  mergeConfig(defaultConfig, userConfig) {
    const merged = { ...defaultConfig }
    
    Object.keys(userConfig).forEach(key => {
      if (typeof userConfig[key] === 'object' && !Array.isArray(userConfig[key])) {
        merged[key] = { ...defaultConfig[key], ...userConfig[key] }
      } else {
        merged[key] = userConfig[key]
      }
    })
    
    return merged
  }

  /**
   * 获取配置
   */
  get(path) {
    const keys = path.split('.')
    let value = this.config
    
    for (const key of keys) {
      if (value && typeof value === 'object') {
        value = value[key]
      } else {
        return undefined
      }
    }
    
    return value
  }

  /**
   * 设置配置
   */
  set(path, value) {
    const keys = path.split('.')
    let current = this.config
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i]
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {}
      }
      current = current[key]
    }
    
    current[keys[keys.length - 1]] = value
    this.saveConfig()
  }

  /**
   * 获取百炼服务配置
   */
  getBailianConfig() {
    return this.get('bailian')
  }

  /**
   * 设置百炼服务配置
   */
  setBailianConfig(config) {
    this.set('bailian', { ...this.get('bailian'), ...config })
  }

  /**
   * 检查百炼服务是否已配置
   */
  isBailianConfigured() {
    const config = this.getBailianConfig()
    return !!(config.apiKey && config.workspaceId && config.enabled)
  }

  /**
   * 获取处理配置
   */
  getProcessingConfig() {
    return this.get('processing')
  }

  /**
   * 设置处理配置
   */
  setProcessingConfig(config) {
    this.set('processing', { ...this.get('processing'), ...config })
  }

  /**
   * 获取分段配置
   */
  getSegmentationConfig() {
    return this.get('segmentation')
  }

  /**
   * 设置分段配置
   */
  setSegmentationConfig(config) {
    this.set('segmentation', { ...this.get('segmentation'), ...config })
  }

  /**
   * 重置为默认配置
   */
  reset() {
    this.config = this.getDefaultConfig()
    this.saveConfig()
  }

  /**
   * 验证配置
   */
  validate() {
    const errors = []
    
    // 验证百炼配置
    const bailian = this.getBailianConfig()
    if (bailian.enabled) {
      if (!bailian.apiKey) {
        errors.push('百炼API Key未配置')
      }
      if (!bailian.workspaceId) {
        errors.push('百炼工作空间ID未配置')
      }
      if (bailian.timeout < 5000) {
        errors.push('百炼服务超时时间过短')
      }
    }

    // 验证处理配置
    const processing = this.getProcessingConfig()
    if (processing.maxFileSize < 1024) {
      errors.push('最大文件大小设置过小')
    }
    if (processing.batchSize < 1 || processing.batchSize > 20) {
      errors.push('批处理大小应在1-20之间')
    }

    // 验证分段配置
    const segmentation = this.getSegmentationConfig()
    if (segmentation.maxLength < 100) {
      errors.push('最大分段长度过小')
    }
    if (segmentation.overlap >= segmentation.maxLength) {
      errors.push('重叠长度不能大于等于最大分段长度')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 导出配置
   */
  export() {
    return {
      config: this.config,
      timestamp: new Date().toISOString(),
      version: '1.0'
    }
  }

  /**
   * 导入配置
   */
  import(data) {
    try {
      if (data.config) {
        this.config = this.mergeConfig(this.getDefaultConfig(), data.config)
        this.saveConfig()
        return true
      }
      return false
    } catch (error) {
      console.error('导入配置失败:', error)
      return false
    }
  }

  /**
   * 获取配置摘要
   */
  getSummary() {
    const bailian = this.getBailianConfig()
    const processing = this.getProcessingConfig()
    
    return {
      bailianEnabled: bailian.enabled,
      useAI: processing.useAI,
      fallbackEnabled: processing.fallbackToLocal,
      maxFileSize: `${Math.round(processing.maxFileSize / 1024 / 1024)}MB`,
      defaultStrategy: this.get('segmentation.defaultStrategy'),
      configured: this.isBailianConfigured()
    }
  }
}

// 创建全局配置实例
const aiConfig = new AIConfig()

export default aiConfig
