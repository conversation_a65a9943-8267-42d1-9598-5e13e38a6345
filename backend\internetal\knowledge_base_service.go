package internetal

import (
	"fmt"

	"gorm.io/gorm"
)

type KnowledgeBaseService struct {
	db *gorm.DB
}

func NewKnowledgeBaseService(db *gorm.DB) *KnowledgeBaseService {
	return &KnowledgeBaseService{db: db}
}

// 创建知识库
func (s *KnowledgeBaseService) CreateKnowledgeBase(kb *KnowledgeBase) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 创建知识库主记录
		if err := tx.Create(kb).Error; err != nil {
			return fmt.Errorf("创建知识库失败: %v", err)
		}

		// 创建设置记录
		if kb.Settings.KnowledgeBaseID == "" {
			kb.Settings.KnowledgeBaseID = kb.ID
		}
		if err := tx.Create(&kb.Settings).Error; err != nil {
			return fmt.Errorf("创建知识库设置失败: %v", err)
		}

		// 创建文件记录
		for i := range kb.Files {
			kb.Files[i].KnowledgeBaseID = kb.ID
			if err := tx.Create(&kb.Files[i]).Error; err != nil {
				return fmt.Errorf("创建知识库文件失败: %v", err)
			}

			// 创建分段记录
			for j := range kb.Files[i].Segments {
				segment := &KnowledgeSegment{
					ID:              kb.Files[i].Segments[j].ID,
					FileID:          kb.Files[i].ID,
					KnowledgeBaseID: kb.ID,
					Content:         kb.Files[i].Segments[j].Content,
					Summary:         kb.Files[i].Segments[j].Summary,
					Length:          kb.Files[i].Segments[j].Length,
					Position:        j + 1,
				}
				// 注意：这里假设kb.Files[i].Segments[j]是某种包含Keywords字段的结构体
				// 如果Keywords是[]string类型，需要进行转换
				// segment.SetKeywords(kb.Files[i].Segments[j].Keywords)

				if err := tx.Create(segment).Error; err != nil {
					return fmt.Errorf("创建知识库分段失败: %v", err)
				}
			}
		}

		return nil
	})
}

// 获取知识库列表
func (s *KnowledgeBaseService) ListKnowledgeBases() ([]KnowledgeBase, error) {
	var knowledgeBases []KnowledgeBase

	err := s.db.Preload("Settings").Preload("Files.Segments").Find(&knowledgeBases).Error
	if err != nil {
		return nil, fmt.Errorf("获取知识库列表失败: %v", err)
	}

	// 转换分段数据格式
	for i := range knowledgeBases {
		for j := range knowledgeBases[i].Files {
			// 将数据库分段转换为API格式
			var segments []map[string]interface{}
			for _, dbSegment := range knowledgeBases[i].Files[j].Segments {
				segment := map[string]interface{}{
					"id":       dbSegment.ID,
					"content":  dbSegment.Content,
					"keywords": dbSegment.GetKeywords(),
					"summary":  dbSegment.Summary,
					"length":   dbSegment.Length,
				}
				segments = append(segments, segment)
			}
			// 这里需要处理类型转换，暂时保持原有结构
		}
	}

	return knowledgeBases, nil
}

// 根据ID获取知识库
func (s *KnowledgeBaseService) GetKnowledgeBaseByID(id string) (*KnowledgeBase, error) {
	var kb KnowledgeBase

	err := s.db.Preload("Settings").Preload("Files.Segments").Where("id = ?", id).First(&kb).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("知识库不存在")
		}
		return nil, fmt.Errorf("获取知识库失败: %v", err)
	}

	return &kb, nil
}

// 更新知识库
func (s *KnowledgeBaseService) UpdateKnowledgeBase(kb *KnowledgeBase) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 更新主记录
		if err := tx.Save(kb).Error; err != nil {
			return fmt.Errorf("更新知识库失败: %v", err)
		}

		// 更新设置
		if err := tx.Save(&kb.Settings).Error; err != nil {
			return fmt.Errorf("更新知识库设置失败: %v", err)
		}

		return nil
	})
}

// 删除知识库
func (s *KnowledgeBaseService) DeleteKnowledgeBase(id string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 由于设置了CASCADE删除，删除主记录会自动删除关联记录
		if err := tx.Where("id = ?", id).Delete(&KnowledgeBase{}).Error; err != nil {
			return fmt.Errorf("删除知识库失败: %v", err)
		}
		return nil
	})
}

// 搜索知识库内容
func (s *KnowledgeBaseService) SearchKnowledgeBase(query string, knowledgeBaseIds []string, maxResults int, minScore float64) ([]KnowledgeSearchResult, error) {
	var segments []KnowledgeSegment

	db := s.db.Model(&KnowledgeSegment{})

	// 如果指定了知识库ID，添加过滤条件
	if len(knowledgeBaseIds) > 0 {
		db = db.Where("knowledge_base_id IN ?", knowledgeBaseIds)
	}

	// 简单的文本搜索（后续可以改进为向量搜索）
	db = db.Where("content LIKE ?", "%"+query+"%").Limit(maxResults)

	if err := db.Find(&segments).Error; err != nil {
		return nil, fmt.Errorf("搜索知识库失败: %v", err)
	}

	// 转换为搜索结果格式
	var results []KnowledgeSearchResult
	for _, segment := range segments {
		result := KnowledgeSearchResult{
			ID:              segment.ID,
			Content:         segment.Content,
			Score:           0.8, // 简单搜索给固定分数
			Source:          fmt.Sprintf("分段_%s", segment.ID),
			KnowledgeBaseId: segment.KnowledgeBaseID,
			Keywords:        segment.GetKeywords(),
			Summary:         segment.Summary,
		}
		results = append(results, result)
	}

	return results, nil
}

// 添加分段
func (s *KnowledgeBaseService) AddSegment(segment *KnowledgeSegment) error {
	if err := s.db.Create(segment).Error; err != nil {
		return fmt.Errorf("添加分段失败: %v", err)
	}

	// 更新知识库的分段计数
	s.updateSegmentCount(segment.KnowledgeBaseID)

	return nil
}

// 更新分段
func (s *KnowledgeBaseService) UpdateSegment(segment *KnowledgeSegment) error {
	if err := s.db.Save(segment).Error; err != nil {
		return fmt.Errorf("更新分段失败: %v", err)
	}
	return nil
}

// 删除分段
func (s *KnowledgeBaseService) DeleteSegment(id string) error {
	var segment KnowledgeSegment
	if err := s.db.Where("id = ?", id).First(&segment).Error; err != nil {
		return fmt.Errorf("分段不存在")
	}

	if err := s.db.Delete(&segment).Error; err != nil {
		return fmt.Errorf("删除分段失败: %v", err)
	}

	// 更新知识库的分段计数
	s.updateSegmentCount(segment.KnowledgeBaseID)

	return nil
}

// 更新知识库分段计数
func (s *KnowledgeBaseService) updateSegmentCount(knowledgeBaseID string) {
	var count int64
	s.db.Model(&KnowledgeSegment{}).Where("knowledge_base_id = ?", knowledgeBaseID).Count(&count)
	s.db.Model(&KnowledgeBase{}).Where("id = ?", knowledgeBaseID).Update("segment_count", count)
}

// 批量更新分段向量
func (s *KnowledgeBaseService) UpdateSegmentEmbeddings(knowledgeBaseID string, embeddings map[string][]float64) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		for segmentID, embedding := range embeddings {
			var segment KnowledgeSegment
			if err := tx.Where("id = ? AND knowledge_base_id = ?", segmentID, knowledgeBaseID).First(&segment).Error; err != nil {
				continue // 跳过不存在的分段
			}

			segment.SetEmbedding(embedding)
			if err := tx.Save(&segment).Error; err != nil {
				return fmt.Errorf("更新分段向量失败: %v", err)
			}
		}
		return nil
	})
}

// 获取知识库统计信息
func (s *KnowledgeBaseService) GetKnowledgeBaseStats(id string) (map[string]interface{}, error) {
	var kb KnowledgeBase
	if err := s.db.Where("id = ?", id).First(&kb).Error; err != nil {
		return nil, fmt.Errorf("知识库不存在")
	}

	var fileCount, segmentCount int64
	s.db.Model(&KnowledgeBaseFile{}).Where("knowledge_base_id = ?", id).Count(&fileCount)
	s.db.Model(&KnowledgeSegment{}).Where("knowledge_base_id = ?", id).Count(&segmentCount)

	stats := map[string]interface{}{
		"id":           kb.ID,
		"name":         kb.Name,
		"fileCount":    fileCount,
		"segmentCount": segmentCount,
		"status":       kb.Status,
		"createdAt":    kb.CreatedAt,
		"updatedAt":    kb.UpdatedAt,
	}

	return stats, nil
}
