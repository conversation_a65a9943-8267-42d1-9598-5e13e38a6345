<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <h1 class="text-3xl font-bold text-gray-900 mb-8 text-center">文档解析测试</h1>
      
      <!-- 文件上传区域 -->
      <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">上传测试文件</h2>
        
        <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer"
             @click="triggerFileUpload"
             @dragover.prevent
             @drop.prevent="handleFileDrop">
          <div class="text-4xl text-gray-400 mb-4">📁</div>
          <p class="text-gray-600 mb-2">点击选择文件或拖拽文件到此处</p>
          <p class="text-sm text-gray-500">支持 TXT、Markdown、Word文档(.docx) 格式</p>
        </div>
        
        <input ref="fileInput" type="file" class="hidden" accept=".txt,.md,.docx" @change="handleFileSelect">
      </div>
      
      <!-- 解析结果 -->
      <div v-if="parseResult" class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">解析结果</h2>
        
        <!-- 文件信息 -->
        <div class="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 class="font-semibold text-gray-900 mb-2">文件信息</h3>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>文件名: {{ parseResult.fileName }}</div>
            <div>文件大小: {{ formatFileSize(parseResult.fileSize) }}</div>
            <div>文件类型: {{ parseResult.fileType }}</div>
            <div>解析时间: {{ parseResult.parseTime }}ms</div>
          </div>
        </div>

        <!-- 文档分析 -->
        <div v-if="parseResult.analysis" class="mb-6 p-4 bg-purple-50 rounded-lg">
          <h3 class="font-semibold text-gray-900 mb-2">AI文档分析</h3>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>文档类型: {{ parseResult.analysis.documentType }}</div>
            <div>结构评分: {{ parseResult.analysis.structureScore }}/10</div>
            <div>质量评分: {{ parseResult.analysis.qualityScore }}/10</div>
            <div>推荐策略: {{ parseResult.analysis.recommendedStrategy }}</div>
          </div>
          <div v-if="parseResult.analysis.mainTopics && parseResult.analysis.mainTopics.length > 0" class="mt-3">
            <span class="text-sm font-medium text-gray-700">主要主题:</span>
            <div class="flex flex-wrap gap-1 mt-1">
              <span v-for="topic in parseResult.analysis.mainTopics" :key="topic"
                    class="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded">
                {{ topic }}
              </span>
            </div>
          </div>
        </div>
        
        <!-- AI配置 -->
        <div v-if="aiEnabled" class="mb-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
          <h3 class="font-semibold text-gray-900 mb-3 flex items-center">
            <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
            AI增强功能
          </h3>
          <div class="flex items-center">
            <input v-model="segmentSettings.useAI" @change="reSegment" type="checkbox" id="useAI"
                   class="w-4 h-4 text-blue-600 border-gray-300 rounded">
            <label for="useAI" class="ml-2 text-sm text-gray-700">启用AI智能分段和内容分析</label>
          </div>
          <p class="text-xs text-gray-600 mt-2">
            AI功能将提供更准确的语义分段、关键词提取和摘要生成
          </p>
        </div>

        <!-- 分段设置 -->
        <div class="mb-6 p-4 bg-blue-50 rounded-lg">
          <h3 class="font-semibold text-gray-900 mb-3">分段设置</h3>
          <div class="grid grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">分段策略</label>
              <select v-model="segmentSettings.strategy" @change="reSegment"
                      class="w-full px-3 py-2 border border-gray-200 rounded text-sm">
                <option value="auto">自动分段</option>
                <option value="semantic">语义分段</option>
                <option value="fixed">固定长度</option>
                <option value="manual">手动分段</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">最大长度</label>
              <input v-model.number="segmentSettings.maxLength" @change="reSegment"
                     type="number" min="100" max="5000"
                     class="w-full px-3 py-2 border border-gray-200 rounded text-sm">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">重叠长度</label>
              <input v-model.number="segmentSettings.overlap" @change="reSegment"
                     type="number" min="0" max="500"
                     class="w-full px-3 py-2 border border-gray-200 rounded text-sm">
            </div>
          </div>
        </div>
        
        <!-- 分段结果 -->
        <div class="mb-6">
          <h3 class="font-semibold text-gray-900 mb-3">
            分段结果 (共 {{ segments.length }} 个分段)
          </h3>
          
          <div class="space-y-4 max-h-96 overflow-y-auto">
            <div v-for="segment in segments" :key="segment.id"
                 class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-center justify-between mb-3">
                <h4 class="font-medium text-gray-900">分段 {{ segment.id }}</h4>
                <div class="flex items-center space-x-3 text-sm text-gray-500">
                  <span>{{ segment.content.length }} 字符</span>
                  <span>{{ segment.keywords.length }} 关键词</span>
                </div>
              </div>
              
              <!-- 分段内容 -->
              <div class="mb-3 p-3 bg-gray-50 rounded text-sm">
                <div class="max-h-32 overflow-y-auto">
                  {{ segment.content }}
                </div>
              </div>
              
              <!-- 关键词 -->
              <div v-if="segment.keywords.length > 0" class="mb-2">
                <span class="text-xs text-gray-600 mr-2">关键词:</span>
                <span v-for="keyword in segment.keywords" :key="keyword"
                      class="inline-block px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded mr-1">
                  {{ keyword }}
                </span>
              </div>
              
              <!-- 摘要 -->
              <div v-if="segment.summary" class="text-xs text-gray-700">
                <span class="text-gray-600">摘要:</span> {{ segment.summary }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 统计信息 -->
        <div class="p-4 bg-green-50 rounded-lg">
          <h3 class="font-semibold text-gray-900 mb-2">统计信息</h3>
          <div class="grid grid-cols-4 gap-4 text-sm">
            <div>总字符数: {{ parseResult.content.length }}</div>
            <div>分段数量: {{ segments.length }}</div>
            <div>平均分段长度: {{ Math.round(parseResult.content.length / segments.length) }}</div>
            <div>总关键词数: {{ totalKeywords }}</div>
          </div>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="isLoading" class="bg-white rounded-xl shadow-lg p-8 text-center">
        <div class="w-16 h-16 mx-auto mb-4 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="text-gray-600">正在解析文档...</p>
      </div>
      
      <!-- 错误信息 -->
      <div v-if="error" class="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
        <div class="text-red-500 text-4xl mb-4">❌</div>
        <h3 class="text-lg font-semibold text-red-900 mb-2">解析失败</h3>
        <p class="text-red-700">{{ error }}</p>
        <button @click="clearError" class="mt-4 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
          重新尝试
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import EnhancedDocumentParser from '../../utils/enhancedDocumentParser.js'
import aiConfig from '../config/aiConfig.js'

export default {
  name: 'DocumentParserTest',
  
  data() {
    return {
      isLoading: false,
      error: null,
      parseResult: null,
      segments: [],
      parser: null,
      aiEnabled: false,
      segmentSettings: {
        strategy: 'auto',
        maxLength: 1000,
        overlap: 100,
        useAI: true
      }
    }
  },

  mounted() {
    this.initializeParser()
  },
  
  computed: {
    totalKeywords() {
      return this.segments.reduce((total, segment) => total + segment.keywords.length, 0)
    }
  },
  
  methods: {
    initializeParser() {
      try {
        this.parser = new EnhancedDocumentParser({
          bailian: aiConfig.getBailianConfig(),
          useAI: this.segmentSettings.useAI,
          fallbackToLocal: true
        })
        this.aiEnabled = aiConfig.isBailianConfigured()
      } catch (error) {
        console.error('初始化解析器失败:', error)
        this.aiEnabled = false
      }
    },

    triggerFileUpload() {
      this.$refs.fileInput.click()
    },
    
    handleFileSelect(event) {
      const file = event.target.files[0]
      if (file) {
        this.parseFile(file)
      }
    },
    
    handleFileDrop(event) {
      const file = event.dataTransfer.files[0]
      if (file) {
        this.parseFile(file)
      }
    },
    
    async parseFile(file) {
      this.isLoading = true
      this.error = null
      this.parseResult = null
      this.segments = []

      try {
        const startTime = Date.now()

        // 使用增强版解析器解析文档
        const result = await this.parser.parseDocument(file, {
          useAI: this.segmentSettings.useAI && this.aiEnabled
        })

        const parseTime = Date.now() - startTime

        // 保存解析结果
        this.parseResult = {
          ...result,
          parseTime: parseTime
        }

        // 生成分段
        this.reSegment()

      } catch (error) {
        console.error('文档解析失败:', error)
        this.error = error.message
      } finally {
        this.isLoading = false
      }
    },
    
    async reSegment() {
      if (!this.parseResult) return

      try {
        this.isLoading = true

        // 使用增强版解析器重新分段
        this.segments = await this.parser.segmentDocument(
          this.parseResult.content,
          {
            ...this.segmentSettings,
            useAI: this.segmentSettings.useAI && this.aiEnabled
          },
          this.parseResult.analysis
        )
      } catch (error) {
        console.error('分段失败:', error)
        this.error = '分段处理失败: ' + error.message
      } finally {
        this.isLoading = false
      }
    },
    
    clearError() {
      this.error = null
    },
    
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
