# 数据库初始化说明

## 概述
此目录包含知识库系统的数据库初始化脚本。

## 文件说明

### init.sql
数据库初始化脚本，包含以下表结构：

1. **knowledge_bases** - 知识库主表
   - 存储知识库的基本信息（ID、名称、描述、类型、状态等）

2. **knowledge_base_settings** - 知识库设置表
   - 存储知识库的配置信息（解析设置、分段策略、AI配置等）

3. **knowledge_base_files** - 知识库文件表
   - 存储上传到知识库的文件信息

4. **knowledge_segments** - 知识库分段表
   - 存储文档分段后的内容、关键词、摘要、向量等信息

5. **robots** - 机器人表
   - 存储聊天机器人的配置信息

## 使用方法

### 1. 创建数据库
```sql
-- 方法1：使用脚本自动创建
mysql -u root -p < init.sql

-- 方法2：手动创建
mysql -u root -p
CREATE DATABASE knowledge_base_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE knowledge_base_system;
SOURCE init.sql;
```

### 2. 配置应用
1. 复制 `backend/cmd/config.yaml.example` 为 `backend/cmd/config.yaml`
2. 修改数据库连接配置：
   ```yaml
   database:
     host: "localhost"
     port: "3306"
     username: "your_username"
     password: "your_password"
     base: "knowledge_base_system"
   ```

### 3. 启动应用
```bash
cd backend/cmd
go run main.go
```

## 注意事项

1. **字符集**：所有表都使用 `utf8mb4` 字符集，支持完整的Unicode字符
2. **外键约束**：设置了级联删除，删除知识库时会自动删除相关的设置、文件和分段
3. **索引优化**：为常用查询字段添加了索引，提高查询性能
4. **示例数据**：应用启动时会自动创建示例数据，无需手动插入

## 表关系图

```
knowledge_bases (主表)
├── knowledge_base_settings (1:1)
├── knowledge_base_files (1:N)
│   └── knowledge_segments (1:N)
└── robots (独立表)
```

## 数据类型说明

- **TEXT**: 短文本内容（< 65KB）
- **LONGTEXT**: 长文本内容（< 4GB），用于存储文件内容和向量数据
- **JSON**: 存储为TEXT，应用层面进行JSON序列化/反序列化
- **TIMESTAMP**: 自动管理创建和更新时间

## 性能优化建议

1. 定期清理过期的分段数据
2. 对大量向量数据考虑使用专门的向量数据库
3. 根据实际查询模式调整索引策略
4. 监控数据库性能，适时进行表优化
